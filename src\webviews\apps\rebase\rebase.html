<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<style nonce="#{cspNonce}">
			@font-face {
				font-family: 'codicon';
				font-display: block;
				src: url('#{webroot}/codicon.ttf?79130123c9d3674a686cf03962523e8a') format('truetype');
			}
		</style>
		<script type="application/javascript" nonce="#{cspNonce}">
			window.webpackResourceBasePath = '#{webroot}/';
		</script>
	</head>

	<body
		class="scrollable preload"
		data-placement="#{placement}"
		data-vscode-context='{ "webview": "#{webviewId}", "webviewInstance": "#{webviewInstanceId}" }'
	>
		<gl-rebase-editor name="RebaseEditor" placement="#{placement}" bootstrap="#{state}"></gl-rebase-editor>
		#{endOfBody}
	</body>
</html>
