import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import type { RebaseEntry, RebaseEntryAction } from '../../../rebase/protocol';

const rebaseActions = ['pick', 'reword', 'edit', 'squash', 'fixup', 'drop'];

@customElement('gl-rebase-entry')
export class GlRebaseEntryElement extends LitElement {
	static override styles = css`
		:host {
			display: contents;
		}

		.entry {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: var(--gk-entry-padding, 7px) 0;
			padding-left: 26px;
			margin: 0 5px 0 0;
			border-radius: 3px;
			position: relative;
		}

		.entry::before {
			display: inline-block;
			content: ' ';
			background-color: var(--color-background);
			border-right: 2px solid var(--color-background--lighten-15);
			height: 100%;
			position: absolute;
			z-index: 0;
			left: 10px;
			top: 0;
			transform: translateX(-50%);
		}

		.entry::after {
			display: inline-block;
			content: ' ';
			background-color: var(--color-background);
			border: 2px solid var(--color-foreground--75);
			border-radius: 50%;
			height: 12px;
			width: 12px;
			left: 2px;
			position: absolute;
			z-index: 2;
		}

		.entry:focus-within {
			outline: 2px solid var(--color-highlight--50);
			outline-offset: -2px;
		}

		.entry--edit::after,
		.entry--reword::after {
			border-color: rgba(0, 153, 0, 1) !important;
			z-index: 3;
		}

		.entry--edit::before,
		.entry--reword::before {
			border-right-color: rgba(0, 153, 0, 1);
		}

		.entry--squash::after,
		.entry--fixup::after {
			display: none;
		}

		.entry--squash::before,
		.entry--fixup::before {
			border-right-color: rgba(212, 153, 0, 1);
		}

		.entry--drop::after {
			display: none;
		}

		.entry--drop::before {
			border-right-color: rgba(153, 0, 0, 1);
		}

		.entry-handle {
			display: inline-block;
			border-left: 2px dotted var(--color-foreground--75);
			border-right: 2px dotted var(--color-foreground--75);
			height: 14px;
			width: 3px;
			margin-left: 10px;
			cursor: ns-resize;
			touch-action: none; /* Disable touch scrolling on handle */
			-webkit-user-drag: none; /* Disable native drag on handle */
		}

		.entry-action {
			flex: auto 0 0;
			margin: 0 10px;
		}

		.entry-message {
			flex: 100% 1 1;
			margin: 0 10px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			width: 0;
		}

		.entry--fixup .entry-message,
		.entry--drop .entry-message {
			text-decoration: line-through;
			opacity: 0.25;
		}

		.entry-avatar {
			flex: auto 0 0;
			margin: 0;
		}

		.entry--squash .entry-avatar,
		.entry--fixup .entry-avatar,
		.entry--drop .entry-avatar {
			opacity: 0.25 !important;
		}

		.entry-date {
			flex: auto 0 0;
			margin: 0 10px;
			opacity: 0.5;
		}

		.entry-sha {
			flex: auto 0 0;
			margin: 0 10px;
			opacity: 0.7;
			text-decoration: none;
			color: inherit;
		}

		.entry--squash .entry-date,
		.entry--fixup .entry-date,
		.entry--drop .entry-date,
		.entry--squash .entry-sha,
		.entry--fixup .entry-sha,
		.entry--drop .entry-sha {
			text-decoration: line-through;
			opacity: 0.25;
		}

		select {
			width: 80px;
			padding: 2px 4px;
			border: 1px solid var(--color-border);
			background-color: var(--color-background);
			color: var(--color-foreground);
		}

		.entry--edit select,
		.entry--reword select {
			border-color: rgba(0, 153, 0, 1) !important;
			outline-color: rgba(0, 153, 0, 1) !important;
		}

		.entry--squash select,
		.entry--fixup select {
			border-color: rgba(212, 153, 0, 1) !important;
			outline-color: rgba(212, 153, 0, 1) !important;
		}

		.entry--drop select {
			border-color: rgba(153, 0, 0, 1) !important;
			outline-color: rgba(153, 0, 0, 1) !important;
		}
	`;

	@property({ type: Object })
	entry!: RebaseEntry;

	@property({ type: Object })
	authors!: Record<string, { author: string; avatarUrl: string }>;

	@property({ type: String })
	commitUrlTemplate!: string;

	private onActionChanged(e: Event) {
		const select = e.target as HTMLSelectElement;
		const action = select.value as RebaseEntryAction;
		this.dispatchEvent(
			new CustomEvent('action-changed', {
				detail: { sha: this.entry.sha, action: action },
				bubbles: true,
				composed: true,
			}),
		);
	}

	private onKeyDown(e: KeyboardEvent) {
		if (e.key === 'Enter' || e.key === ' ') {
			const select = this.renderRoot.querySelector('select');
			select?.focus();
		}
	}

	override render() {
		const { commit } = this.entry;
		const author = commit ? this.authors[commit.author] : undefined;
		const committer = commit ? this.authors[commit.committer] : undefined;

		return html`
			<li
				class="entry entry--${this.entry.action}"
				data-sha=${this.entry.sha}
				tabindex="0"
				@keydown=${this.onKeyDown}
			>
				<span class="entry-handle"></span>
				<div class="entry-action">
					<select data-sha=${this.entry.sha} @change=${this.onActionChanged}>
						${rebaseActions.map(
							action => html`
								<option value=${action} ?selected=${this.entry.action === action}>${action}</option>
							`,
						)}
					</select>
				</div>

				<span class="entry-message" title=${this.entry.message}> ${this.entry.message} </span>

				${author?.avatarUrl
					? html`
							<gk-avatar-group class="entry-avatar">
								<gk-avatar
									src=${author.avatarUrl}
									title=${committer?.author !== author.author
										? `Authored by: ${author.author}`
										: author.author}
								></gk-avatar>
								${committer?.avatarUrl && committer.author !== author.author
									? html`
											<gk-avatar
												src=${committer.avatarUrl}
												title=${`Committed by: ${committer.author}`}
											></gk-avatar>
									  `
									: nothing}
							</gk-avatar-group>
					  `
					: nothing}
				${commit?.dateFromNow
					? html` <span class="entry-date" title=${commit.date ?? ''}> ${commit.dateFromNow} </span> `
					: nothing}

				<a class="entry-sha icon--commit" href=${this.commitUrlTemplate.replace(/\${commit}/, this.entry.sha)}>
					${this.entry.sha.substring(0, 7)}
				</a>
			</li>
		`;
	}
}

declare global {
	interface HTMLElementTagNameMap {
		'gl-rebase-entry': GlRebaseEntryElement;
	}
}
