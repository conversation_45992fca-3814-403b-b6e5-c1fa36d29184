import './rebase.scss';
import { Avatar, AvatarGroup, defineGkElement } from '@gitkraken/shared-web-components';
import { html, LitElement, nothing } from 'lit';
import { customElement, property, query, state } from 'lit/decorators.js';
import Sortable from 'sortablejs';
import type { Author, RebaseEntry, RebaseEntryAction, State } from '../../rebase/protocol';
import {
	AbortCommand,
	ChangeEntryCommand,
	DisableCommand,
	MoveEntryCommand,
	ReorderCommand,
	SearchCommand,
	StartCommand,
	SwitchCommand,
	UpdateSelectionCommand,
} from '../../rebase/protocol';
import { GlApp } from '../shared/app';
import './components/rebase-entry';
import type { HostIpc } from '../shared/ipc';
import { rebaseStyles } from './rebase.css';
import { RebaseStateProvider } from './stateProvider';

const rebaseActions = ['pick', 'reword', 'edit', 'squash', 'fixup', 'drop'];
const rebaseActionsMap = new Map<string, RebaseEntryAction>([
	['p', 'pick'],
	['P', 'pick'],
	['r', 'reword'],
	['R', 'reword'],
	['e', 'edit'],
	['E', 'edit'],
	['s', 'squash'],
	['S', 'squash'],
	['f', 'fixup'],
	['F', 'fixup'],
	['d', 'drop'],
	['D', 'drop'],
]);

@customElement('gl-rebase-editor')
export class GlRebaseEditor extends GlApp<State> {
	static override shadowRootOptions: ShadowRootInit = { ...LitElement.shadowRootOptions, delegatesFocus: true };

	static override styles = [rebaseStyles];

	@query('#entries')
	private entriesContainer?: HTMLElement;

	@property({ type: Boolean })
	ascending = false;

	// @state()
	// private commitTokenRegex = new RegExp(encodeURIComponent('${commit}'));

	protected override createStateProvider(state: State, ipc: HostIpc) {
		return new RebaseStateProvider(this, state, ipc);
	}

	override connectedCallback(): void {
		super.connectedCallback();

		defineGkElement(Avatar, AvatarGroup);
		document.addEventListener('keydown', this.onDocumentKeyDown);
	}

	override disconnectedCallback(): void {
		document.removeEventListener('keydown', this.onDocumentKeyDown);

		super.disconnectedCallback();
	}

	protected override firstUpdated(): void {
		this.initializeSortable();
	}

	private initializeSortable(): void {
		if (!this.entriesContainer) return;

		Sortable.create(this.entriesContainer, {
			animation: 150,
			handle: '.entry-handle',
			filter: '.entry--base',
			dragClass: 'entry--drag',
			ghostClass: 'entry--dragging',
			onChange: () => this.onSortableChange(),
			onEnd: e => this.onSortableEnd(e),
			// Add these new options for proper web component handling
			forceFallback: true, // Force fallback mode for better shadow DOM compatibility
			fallbackOnBody: true, // Append ghost element to body for better control
			// The elements are still direct children of the container, just rendered by web components
			draggable: 'li', // Target the outer li elements
			setData: (dataTransfer, dragEl) => {
				// Ensure proper drag image with shadow DOM content
				dataTransfer.setDragImage(dragEl, 0, 0);
			},
		});
	}

	private onDocumentKeyDown = (e: KeyboardEvent) => {
		if (e.ctrlKey || e.metaKey) {
			if (e.key === 'Enter' || e.key === 'r') {
				e.preventDefault();
				this.onStartClicked();
			} else if (e.key === 'a') {
				e.preventDefault();
				this.onAbortClicked();
			}
		} else if (e.key === '/') {
			e.preventDefault();
			this.onSearch();
		}
	};

	private onSortableChange() {
		let squashing = false;
		let squashToHere = false;

		const $entries = [...(this.entriesContainer?.querySelectorAll<HTMLLIElement>('li[data-sha]') ?? [])];
		if (this.ascending) {
			$entries.reverse();
		}

		for (const $entry of $entries) {
			squashToHere = false;
			if ($entry.classList.contains('entry--squash') || $entry.classList.contains('entry--fixup')) {
				squashing = true;
			} else if (squashing && !$entry.classList.contains('entry--drop')) {
				squashToHere = true;
				squashing = false;
			}

			$entry.classList.toggle('entry--squash-to', squashToHere && !$entry.classList.contains('entry--base'));
		}
	}

	private onSortableEnd(e: Sortable.SortableEvent) {
		if (e.newIndex == null || e.newIndex === e.oldIndex) return;

		const sha = e.item.dataset.sha;
		if (!sha) return;

		let indexTarget = e.newIndex;
		if (this.ascending && e.oldIndex != null) {
			indexTarget = this.getEntryIndex(sha) + (indexTarget - e.oldIndex) * -1;
		}

		this.moveEntry(sha, indexTarget, false);
		this.setSelectedEntry(sha);
	}

	private getEntryIndex(sha: string): number {
		return this.state?.entries.findIndex(e => e.sha === sha) ?? -1;
	}

	private moveEntry(sha: string, index: number, relative: boolean) {
		this._ipc.sendCommand(MoveEntryCommand, { sha: sha, to: index, relative: relative });
	}

	private setSelectedEntry(sha: string) {
		this._ipc.sendCommand(UpdateSelectionCommand, { sha: sha });
	}

	private onStartClicked() {
		this._ipc.sendCommand(StartCommand, undefined);
	}

	private onAbortClicked() {
		this._ipc.sendCommand(AbortCommand, undefined);
	}

	private onDisableClicked() {
		this._ipc.sendCommand(DisableCommand, undefined);
	}

	private onSwitchClicked() {
		this._ipc.sendCommand(SwitchCommand, undefined);
	}

	private onSearch() {
		this._ipc.sendCommand(SearchCommand, undefined);
	}

	private onOrderChanged(e: Event) {
		const target = e.target as HTMLInputElement;
		this._ipc.sendCommand(ReorderCommand, { ascending: target.checked });
	}

	private onActionChanged(sha: string, action: RebaseEntryAction) {
		this._ipc.sendCommand(ChangeEntryCommand, { sha: sha, action: action });
	}

	override render() {
		return html`
			<div class="container">
				<header>
					<h2>GitLens Interactive Rebase</h2>
					<h4 id="subhead">${this.renderSubhead()}</h4>
					<div class="toggle">
						<input
							class="toggle__input sr-only"
							tabindex="1"
							@change=${this.onOrderChanged}
							.checked=${this.ascending}
							id="ordering"
							name="ordering"
							type="checkbox"
						/>
						<span aria-hidden="true" class="toggle__indicator"></span>
						<label class="toggle__label" for="ordering">Oldest first</label>
					</div>
				</header>

				<div class="entries-container scrollable">
					<ul id="entries" class="entries scrollable">
						${this.state.entries.length === 0 ? this.renderEmptyState() : this.renderEntries()}
					</ul>
				</div>

				<footer>${this.renderShortcuts()} ${this.renderActions()}</footer>
			</div>
		`;
	}

	private renderSubhead() {
		if (!this.state) return nothing;

		return html`
			<span class="icon--branch mr-1">${this.state.branch}</span>
			Rebasing ${this.state.entries.length} commit${this.state.entries.length !== 1 ? 's' : ''}
			${this.state.onto ? ' onto' : nothing}
			${this.state.onto ? html`<span class="icon--commit">${this.state.onto.sha}</span>` : nothing}
		`;
	}

	private renderEmptyState() {
		return html`<li><h3>No commits to rebase</h3></li>`;
	}

	private renderEntries() {
		return this.state.entries.map(
			entry => html`
				<gl-rebase-entry
					.entry=${entry}
					.authors=${this.state.authors}
					.commitUrlTemplate=${this.state.commands.commit}
					@action-changed=${(e: CustomEvent) => this.onActionChanged(e.detail.sha, e.detail.action)}
				></gl-rebase-entry>
			`,
		);
	}

	private renderShortcuts() {
		return html`
			<div class="shortcuts">
				<span class="shortcut"><kbd>p</kbd><span>Pick</span></span>
				<span class="shortcut"><kbd>r</kbd><span>Reword</span></span>
				<!-- ...other shortcuts... -->
			</div>
		`;
	}

	private renderActions() {
		return html`
			<div class="actions">
				<div class="actions--left">
					<button class="button button--flat-subtle" @click=${this.onDisableClicked}>
						Disable Rebase Editor
						<span class="shortcut">Will Abort Rebase</span>
					</button>
					<button class="button button--flat-subtle" @click=${this.onSwitchClicked}>Switch to Text</button>
				</div>
				<div class="actions--right">
					<button
						class="button button--flat-primary"
						@click=${this.onStartClicked}
						?disabled=${this.state.entries.length === 0}
					>
						Start Rebase
						<span class="shortcut">Ctrl+Enter</span>
					</button>
					<button class="button button--flat-secondary" @click=${this.onAbortClicked}>
						Abort
						<span class="shortcut">Ctrl+A</span>
					</button>
				</div>
			</div>
		`;
	}
}
