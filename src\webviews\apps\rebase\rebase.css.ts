import { css } from 'lit';

export const rebaseStyles = css`
	:host {
		--gk-avatar-size: 2.2rem;
		overflow: overlay;
	}

	:host-context(.vscode-dark),
	:host-context(.vscode-high-contrast:not(.vscode-high-contrast-light)) {
		--avatar-bg: var(--color-background--lighten-30);
	}
	:host-context(.vscode-light) {
		--avatar-bg: var(--color-background--darken-30);
	}
	:host-context(.vscode-high-contrast-light) {
		--avatar-bg: var(--color-foreground--50);
	}

	.container {
		display: grid;
		grid-template-areas: 'header' 'entries' 'footer';
		grid-template-rows: auto 1fr auto;
		height: 100vh;
		margin: 0 auto;
		max-width: 1200px;
		min-width: 495px;
	}

	header {
		grid-area: header;
		display: flex;
		align-items: baseline;
		flex-wrap: wrap;
		margin: 0;
		gap: 0 0.5em;
		position: relative;
	}

	header h2 {
		flex: auto 0 1;
		margin-top: 0.5em;
		margin-right: 1em;
	}

	header h4 {
		flex: auto 1 1;
		margin-top: 0;
	}

	header h3,
	header h4#subhead-status {
		flex: 100% 1 1;
		margin: 0.25em 0 0.5em 0;
	}

	header > *[class^='icon--']::before,
	header > *[class*=' icon--']::before {
		opacity: 0.7;
		font-size: 1.4em;
		top: 5px;
		line-height: 0.6em;
	}

	h4 {
		font-size: 1.4rem;
		opacity: 0.8;
	}

	h4#subhead {
		padding-right: 12rem;
	}

	footer {
		grid-area: footer;
		display: grid;
		grid-template-areas: 'shortcuts' 'actions';
		border-top: 1px solid var(--vscode-sideBarSectionHeader-border);
	}

	.entries-container {
		grid-area: entries;
		overflow: auto;
	}

	.entries {
		padding: 0;
	}

	.entries--empty {
		border: none;
	}

	.entries--empty h3 {
		margin: 1em auto;
		width: fit-content;
	}

	.shortcuts {
		grid-area: shortcuts;
		display: flex;
		justify-content: center;
		text-align: center;
	}

	.actions {
		grid-area: actions;
		margin: 0.5rem 0 1rem 0;
		display: flex;
		flex-wrap: wrap;
	}

	.actions--left {
		display: flex;
		gap: 0 1rem;
	}

	.actions--right {
		display: flex;
		margin-left: auto;
		gap: 0 1rem;
	}

	.button {
		letter-spacing: 0.1rem;
		margin: 0;
		padding: 0.5rem 1rem;
	}

	.button .shortcut {
		margin: 0.1rem 0 0 0;
	}

	.shortcut {
		display: inline-block;
		margin: 5px 10px 5px 0;
		opacity: 0.6;
	}

	.shortcut span {
		margin: 0 0 0 5px;
	}

	.toggle {
		display: inline-flex;
		flex-direction: row-reverse;
		align-items: center;
		gap: 0.5em;
		position: absolute;
		top: 2rem;
		right: 0;
	}

	.toggle__input,
	.toggle__indicator {
		width: 2.4em;
		height: 1.4em;
	}

	.toggle__input {
		position: absolute;
		appearance: none;
		opacity: 0;
		border-radius: 1em;
		padding: 0;
	}

	.toggle__input:focus {
		border-radius: 1em;
	}

	.toggle__indicator {
		position: relative;
		pointer-events: none;
		display: block;
		flex: none;
		border-radius: 1em;
	}

	:host-context(.vscode-dark) .toggle__indicator,
	:host-context(.vscode-high-contrast:not(.vscode-high-contrast-light)) .toggle__indicator {
		background-color: var(--color-background--lighten-075);
		border: 1px solid var(--color-background--lighten-075);
	}

	:host-context(.vscode-light) .toggle__indicator {
		background-color: var(--color-background--darken-075);
		border: 1px solid var(--color-background--darken-075);
	}

	:host-context(.vscode-high-contrast) .toggle__indicator {
		background-color: none;
		border: 1px solid var(--color-foreground);
	}

	.toggle__indicator::before {
		content: '';
		top: 0.1em;
		left: 0.1em;
		position: absolute;
		width: 1.2em;
		height: 1.2em;
		border-radius: 100%;
		background-color: var(--color-button-foreground);
	}

	.vscode-high-contrast-light .toggle__input:not(:checked) ~ .toggle__indicator::before {
		background-color: var(--color-foreground);
	}

	.toggle__input:checked ~ .toggle__indicator::before {
		transform: translateX(1em);
	}

	.toggle__input:checked ~ .toggle__indicator {
		background-color: var(--color-highlight);
	}

	.toggle__input:focus ~ .toggle__indicator {
		border-color: var(--color-background);
		box-shadow: 0 0 0 1px var(--color-focus-border);
	}

	.scrollable {
		overflow: auto;
	}
`;
